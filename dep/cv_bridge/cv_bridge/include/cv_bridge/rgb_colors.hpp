/*********************************************************************
 * Original color definition is at scikit-image distributed with
 * following license disclaimer:
 *
 * Copyright (C) 2011, the scikit-image team
 * Copyright (c) 2018 Intel Corporation.
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 *   1. Redistributions of source code must retain the above copyright
 *      notice, this list of conditions and the following disclaimer.
 *   2. Redistributions in binary form must reproduce the above copyright
 *      notice, this list of conditions and the following disclaimer in
 *      the documentation and/or other materials provided with the
 *      distribution.
 *   3. Neither the name of skimage nor the names of its contributors may be
 *      used to endorse or promote products derived from this software without
 *      specific prior written permission.
 *
 *  THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 *  IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *  WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 *  DISCLAIMED. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT,
 *  INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *  (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 *  SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 *  HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 *  STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 *  IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *  POSSIBILITY OF SUCH DAMAGE.
 *********************************************************************/

#ifndef CV_BRIDGE__RGB_COLORS_HPP_
#define CV_BRIDGE__RGB_COLORS_HPP_

#include <opencv2/opencv.hpp>
// #include <cv_bridge/cv_bridge_export.h>
#include "cv_bridge/visibility_control.h"

namespace cv_bridge
{
  namespace rgb_colors
  {
    /**
     * @brief
     * 146 rgb colors
     */
    enum Colors
    {
      ALICEBLUE,
      ANTIQUEWHITE,
      AQUA,
      AQUAMARINE,
      AZURE,
      BEIGE,
      BISQUE,
      BLACK,
      BLANCHEDALMOND,
      BLUE,
      BLUEVIOLET,
      BROWN,
      BURLYWOOD,
      CADETBLUE,
      CHARTREUSE,
      CHOCOLATE,
      CORAL,
      CORNFLOWERBLUE,
      CORNSILK,
      CRIMSON,
      CYAN,
      DARKBLUE,
      DARKCYAN,
      DARKGOLDENROD,
      DARKGRAY,
      DARKGREEN,
      DARKGREY,
      DARKKHAKI,
      DARKMAGENTA,
      DARKOLIVEGREEN,
      DARKORANGE,
      DARKORCHID,
      DARKRED,
      DARKSALMON,
      DARKSEAGREEN,
      DARKSLATEBLUE,
      DARKSLATEGRAY,
      DARKSLATEGREY,
      DARKTURQUOISE,
      DARKVIOLET,
      DEEPPINK,
      DEEPSKYBLUE,
      DIMGRAY,
      DIMGREY,
      DODGERBLUE,
      FIREBRICK,
      FLORALWHITE,
      FORESTGREEN,
      FUCHSIA,
      GAINSBORO,
      GHOSTWHITE,
      GOLD,
      GOLDENROD,
      GRAY,
      GREEN,
      GREENYELLOW,
      GREY,
      HONEYDEW,
      HOTPINK,
      INDIANRED,
      INDIGO,
      IVORY,
      KHAKI,
      LAVENDER,
      LAVENDERBLUSH,
      LAWNGREEN,
      LEMONCHIFFON,
      LIGHTBLUE,
      LIGHTCORAL,
      LIGHTCYAN,
      LIGHTGOLDENRODYELLOW,
      LIGHTGRAY,
      LIGHTGREEN,
      LIGHTGREY,
      LIGHTPINK,
      LIGHTSALMON,
      LIGHTSEAGREEN,
      LIGHTSKYBLUE,
      LIGHTSLATEGRAY,
      LIGHTSLATEGREY,
      LIGHTSTEELBLUE,
      LIGHTYELLOW,
      LIME,
      LIMEGREEN,
      LINEN,
      MAGENTA,
      MAROON,
      MEDIUMAQUAMARINE,
      MEDIUMBLUE,
      MEDIUMORCHID,
      MEDIUMPURPLE,
      MEDIUMSEAGREEN,
      MEDIUMSLATEBLUE,
      MEDIUMSPRINGGREEN,
      MEDIUMTURQUOISE,
      MEDIUMVIOLETRED,
      MIDNIGHTBLUE,
      MINTCREAM,
      MISTYROSE,
      MOCCASIN,
      NAVAJOWHITE,
      NAVY,
      OLDLACE,
      OLIVE,
      OLIVEDRAB,
      ORANGE,
      ORANGERED,
      ORCHID,
      PALEGOLDENROD,
      PALEGREEN,
      PALEVIOLETRED,
      PAPAYAWHIP,
      PEACHPUFF,
      PERU,
      PINK,
      PLUM,
      POWDERBLUE,
      PURPLE,
      RED,
      ROSYBROWN,
      ROYALBLUE,
      SADDLEBROWN,
      SALMON,
      SANDYBROWN,
      SEAGREEN,
      SEASHELL,
      SIENNA,
      SILVER,
      SKYBLUE,
      SLATEBLUE,
      SLATEGRAY,
      SLATEGREY,
      SNOW,
      SPRINGGREEN,
      STEELBLUE,
      TAN,
      TEAL,
      THISTLE,
      TOMATO,
      TURQUOISE,
      VIOLET,
      WHEAT,
      WHITE,
      WHITESMOKE,
      YELLOW,
      YELLOWGREEN,
    };

    /**
     * @brief
     * get rgb color with enum.
     */
    CV_BRIDGE_EXPORT cv::Vec3d getRGBColor(const int color);

  }  // namespace rgb_colors

}  // namespace cv_bridge

#endif  // CV_BRIDGE__RGB_COLORS_HPP_
