#VRML_SIM R2025a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/kuka/youbot/protos/Youbot.proto
# keywords: robot/wheeled
# The KUKA youBot is a powerful, educational robot that is especially designed for research and education in mobile manipulation, which counts as a key technology for professional service robotics. It consists of an omnidirectional platform, a five degree-of-freedom robot arm and a two-finger gripper.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/BodyMesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm0Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm1Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm2Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm3Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm4Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/Arm5Mesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/FingerMesh.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/InteriorWheel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/ExteriorWheel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/robots/kuka/youbot/protos/PlateMesh.proto"

EXTERNPROTO "sensor_protos/IMU.proto"
EXTERNPROTO "sensor_protos/RGBD.proto"

PROTO MecanumBotRGBD [
  field SFVec3f    translation     0 0 0                 # Is `Pose.translation`.
  field SFRotation rotation        0 1 0 0               # Is `Pose.rotation`.
  field SFString   name            "MecanumBotRGBD"    # Is `Solid.name`.
  field SFString   controller      "<generic>"           # Is `Robot.controller`.
  field MFString   controllerArgs  []                    # Is `Robot.controllerArgs`.
  field SFString   customData      ""                    # Is `Robot.customData`.
  field SFBool     supervisor      TRUE                 # Is `Robot.supervisor`.
  field SFBool     synchronization TRUE                  # Is `Robot.synchronization`.
  field SFFloat    wheelRadius     0.05
  field SFFloat    wheelThickness    0.03
  field SFFloat    wheelToCenterDis      0.2
  field MFNode     extensionSlot   [
  ]  # Extends the robot with new nodes in the extension slot.
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    children [
      Solid {
        translation 0 0 0.7
        children [
          # 固定腰部的相机
          DEF Waist_Back_RGBD_Depth RangeFinder {
            translation -0.1 0 0.0
            rotation 0.382057312895724 0 -0.9241386311927983 3.14
            name "waist_back_rgbd_depth"
            fieldOfView 1.22
            width 640
            height 480
            minRange 0.15
            maxRange 10.01
          }
          DEF Waist_Back_RGBD_RGB Camera {
            translation -0.1 0 0.0
            rotation 0.382057312895724 0 -0.9241386311927983 3.14
            name "waist_back_rgbd_rgb"
            fieldOfView 1.22
            width 640
            height 480
          }
          DEF Waist_Front_RGBD_Depth RangeFinder {
            translation 0.1 0 0.0
            rotation 0 1 0 0.611
            name "waist_front_rgbd_depth"
            fieldOfView 1.22
            width 640
            height 480
            minRange 0.15
            maxRange 10.01
          }
          DEF Waist_Front_RGBD_RGB Camera {
            translation 0.1 0 0.0
            rotation 0 1 0 0.611
            name "waist_front_rgbd_rgb"
            fieldOfView 1.22
            width 640
            height 480
          }

          # 设置头部相机，添加两自由度关节
          HingeJoint {
            jointParameters HingeJointParameters {
              position -3.3734082194314396e-10
              axis 0 1 0
              anchor 0 0 0.5
            }
            device [
              RotationalMotor {
                name "head_pitch_joint"
                maxVelocity 1000
                minPosition -3.14
                maxPosition 3.14
                maxTorque 1000
              }
              PositionSensor {
                name "head_pitch_joint_sensor"
              }
            ]
            endPoint Solid {
              translation 0.0 0.0 0.5
              children [
                Shape {
                  appearance PBRAppearance {
                  }
                  geometry Box {
                    size 0.1 0.05 0.05
                  }
                }
                HingeJoint {
                  jointParameters HingeJointParameters {
                    position -8.785180135825542e-12
                    axis 0 0 1
                    anchor 0 0.0196 0.084
                  }
                  device [
                    RotationalMotor {
                      name "head_yaw_joint"
                      maxVelocity 1000
                      minPosition -3.14
                      maxPosition 3.14
                      maxTorque 1000
                    }
                    PositionSensor {
                      name "head_yaw_joint_sensor"
                    }
                  ]
                  endPoint Solid {
                    translation 0.0 0.0 0.0
                    children [
                      DEF Head_Back_RGBD_Depth RangeFinder {
                        translation -0.1 0 0
                        rotation 0 0 1 -3.14
                        name "head_back_rgbd_depth"
                        fieldOfView 1.22
                        width 640
                        height 480
                        minRange 0.15
                        maxRange 10.01
                      }
                      DEF Head_Back_RGBD_RGB Camera {
                        translation -0.1 0 0
                        rotation 0 0 1 -3.14
                        name "head_back_rgbd_rgb"
                        fieldOfView 1.22
                        width 640
                        height 480
                      }
                      InertialUnit {
                        translation 0.1 0.07 -0.05
                        rotation 0.5773502691896258 -0.5773502691896258 0.5773502691896258 -2.09
                        name "imu_inertialunit"
                      }
                      Accelerometer {
                        translation 0.1 0.07 -0.05
                        rotation 0.5773502691896258 -0.5773502691896258 0.5773502691896258 -2.09
                        name "imu_accelerometer"
                      }
                      Gyro {
                        translation 0.1 0.07 -0.05
                        rotation 0.5773502691896258 -0.5773502691896258 0.5773502691896258 -2.09
                        name "imu_gyro"
                      }
                      DEF Head_Right_Fisheye Camera {
                        translation 0 -0.09 -0.05
                        rotation 0 0 1 -1.58
                        name "fisheye_right"
                        fieldOfView 1.57
                        width 640
                        height 480
                      }
                      DEF Head_Left_Fisheye Camera {
                        translation 0 0.09 -0.05
                        rotation 0 0 1 1.58
                        name "fisheye_left"
                        fieldOfView 1.57
                        width 640
                        height 480
                      }
                      DEF Stereo_Right_RGB Camera {
                        translation 0.1 -0.07 -0.05
                        name "stereo_camera_right"
                        fieldOfView 1.57
                        width 640
                        height 480
                      }
                      DEF Stereo_Left_RGB Camera {
                        translation 0.1 0.07 -0.05
                        name "stereo_camera_left"
                        fieldOfView 1.57
                        width 640
                        height 480
                      }
                  
                      DEF Head_Front_RGBD_Depth RangeFinder {
                        translation 0.1 0 0
                        rotation 0 1 0 0.349
                        name "head_front_rgbd_depth"
                        fieldOfView 1.22
                        width 640
                        height 480
                        minRange 0.15
                        maxRange 10.01
                      }
                      DEF Head_Front_RGBD_RGB Camera {
                        translation 0.1 0 0
                        rotation 0 1 0 0.349
                        name "head_front_rgbd_rgb"
                        fieldOfView 1.22
                        width 640
                        height 480
                      }
                      Shape {
                        appearance PBRAppearance {
                        }
                        geometry Box {
                          size 0.05 0.1 0.05
                        }
                      }
                    ]
                    name "head_yaw_link"
                    physics Physics {
                      density -1
                      mass 1.5869
                      centerOfMass [
                        -0.002261 9.9e-05 -0.000626
                      ]
                      inertiaMatrix [
                        0.0035943 0.0030214 0.0021698
                        -1.9256e-07 0.00035192 1.7748e-06
                      ]
                    }
                  }
                }
              ]
              name "head_pitch_link"
              physics Physics {
                density -1
                mass 0.28765
                centerOfMass [
                  0.000527 0.017913 0.057029
                ]
                inertiaMatrix [
                  0.00013796 0.00011601 9.9509e-05
                  -2.1509e-07 -2.2775e-09 -2.1725e-05
                ]
              }
            }
          }

          GPS {
            translation 0 0 0.25
            name "global_gps"
          }
          Shape {
            appearance PBRAppearance {
            }
            geometry Box {
              size 0.1 0.1 0.1
            }
          }
        ]
        name "sensor_base"
        physics Physics {
          density 10
          mass 2
        }
      }
      BodyMesh {
      }
      Group {
      }
      DEF WHEEL1 InteriorWheel {
        translation 0.2281268273939244 -0.15800001126722663 -0.054698220116484814
        anchor 0.228 -0.158 -0.055
        name "wheel1"
        sensorName "wheel1sensor"
      }
      DEF WHEEL2 ExteriorWheel {
        translation 0.228121289171915 0.1579999838153725 -0.0546946118169235
        anchor 0.228 0.158 -0.055
        name "wheel2"
        sensorName "wheel2sensor"
      }
      DEF WHEEL3 ExteriorWheel {
        translation -0.2281490864288505 -0.1580000069581775 -0.05479149063522682
        anchor -0.228 -0.158 -0.055
        name "wheel3"
        sensorName "wheel3sensor"
      }
      DEF WHEEL4 InteriorWheel {
        translation -0.2282544205456349 0.15799998544279248 -0.05495983827422764
        anchor -0.228 0.158 -0.055
        name "wheel4"
        sensorName "wheel4sensor"
      }
      DEF PLATE Solid {
        translation -0.155 0 0
        children [
          PlateMesh {
          }
        ]
        name "plate"
        boundingObject Group {
          children [
            Pose {
              translation 0.008 0 0.045
              children [
                Box {
                  size 0.25 0.17 0.007
                }
              ]
            }
            Pose {
              translation -0.015 0.112 0.045
              rotation 0 0 1 -0.13
              children [
                Box {
                  size 0.17 0.08 0.007
                }
              ]
            }
            Pose {
              translation -0.015 -0.112 0.045
              rotation 0 0 1 0.13
              children [
                Box {
                  size 0.17 0.08 0.007
                }
              ]
            }
            Pose {
              translation 0.076 0.084 0.045
              rotation 0 0 1 0.81
              children [
                Box {
                  size 0.08 0.08 0.007
                }
              ]
            }
            Pose {
              translation 0.076 -0.084 0.045
              rotation 0 0 1 -0.81
              children [
                Box {
                  size 0.08 0.08 0.007
                }
              ]
            }
          ]
        }
        physics Physics {
          density -1
          mass 0.5
        }
      }
    ]
    name "youBot"
    model "KUKA youBot"
    description "KUKA youBot"
    boundingObject Group {
      children [
        Pose {
          translation 0 0 -0.045
          children [
            Box {
              size 0.34 0.34 0.09
            }
          ]
        }
        Pose {
          translation 0 0 -0.045
          children [
            Box {
              size 0.56 0.23 0.09
            }
          ]
        }
      ]
    }
    physics Physics {
      density -1
      mass 22
      centerOfMass [
        0 0 -0.045
      ]
      inertiaMatrix [
        0.166204 0.418086 0.55459
        0 0 0
      ]
    }
    supervisor TRUE
    linearVelocity -2.0343131073971167e-09 3.647713679324726e-09 -6.219389637853779e-10
    angularVelocity 1.1179644446295345e-09 1.2947391223631242e-10 -1.2834758629551616e-08
  }
}
