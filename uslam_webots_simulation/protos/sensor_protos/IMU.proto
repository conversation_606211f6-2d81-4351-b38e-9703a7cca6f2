#VRML_SIM R2025a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/devices/orbbec/protos/Astra.proto
# keywords: sensor/Accelerometer sensor/Gyro sensor/InertialUnit
# Model of the IMU.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/MattePaint.proto"

PROTO IMU [
  field SFFloat    translationX      0
  field SFFloat    translationY      0
  field SFFloat    translationZ      0
  field SFFloat    rotationX      0
  field SFFloat    rotationY      0
  field SFFloat    rotationZ      0
  field SFFloat    rotationW      0
  
  field SFVec3f    translation   0 0 0
  field SFRotation rotation      0 0 1 0
  field SFString   name          "imu"
]
{
  Solid {
    translation IS translation
    rotation IS rotation
    children [
      Pose {
        translation 0.0 0.0 0.0
        children[
          Shape {
            appearance MattePaint {
              baseColor 0.00 1.0 0.00
            }
            geometry Box {
              size 0.05 0.05 0.02
            }
          }
        ]
      }
      Accelerometer {
        translation %<= (fields.translationX.value) >% %<= (fields.translationY.value) >% %<= (fields.translationZ.value) >%
        rotation %<= (fields.rotationX.value) >% %<= (fields.rotationY.value) >% %<= (fields.rotationZ.value) >% %<= (fields.rotationW.value) >%
        name %<= '"' + fields.name.value + '_accelerometer"' >%
      }
      Gyro {
        name %<= '"' + fields.name.value + '_gyro"' >%
      }
      InertialUnit {
        name %<= '"' + fields.name.value + '_inertialunit"' >%
      }
    ]
    name IS name
    boundingObject Group {
      children [
        Pose {
          translation 0.00000 0.000000 0.0
          children [
            Box {
              size 0.05 0.05 0.02
            }
          ]
        }

      ]
    }
    physics Physics {
      density -1
      mass 0.19
    }
  }
}
