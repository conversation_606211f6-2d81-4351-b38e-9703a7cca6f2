#VRML_SIM R2025a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/devices/orbbec/protos/Astra.proto
# keywords: sensor/camera
# Model of the stereo camera.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2025a/projects/appearances/protos/MattePaint.proto"

PROTO StereoCamera [
  field SFFloat    translationX      0
  field SFFloat    translationY      0
  field SFFloat    translationZ      0
  field SFFloat    rotationX      0
  field SFFloat    rotationY      0
  field SFFloat    rotationZ      0
  field SFFloat    rotationW      0
  field SFVec3f    translation   0 0 0
  field SFRotation rotation      0 0 1 0
  field SFFloat    translationYLeft      0.07
  field SFFloat    translationYRight     -0.07
  field SFString   name          "stereo"
  field SFFloat    colorNoise    0.0     # Defines the `noise` field of the `Camera`.
]
{
  Solid {
    translation IS translation
    rotation IS rotation
    children [
      Pose {
        translation %<= (fields.translationX.value) >% %<= (fields.translationYLeft.value) >% %<= (fields.translationZ.value) >%
        children[
          Shape {
            appearance MattePaint {
              baseColor 1 1 1
            }
            geometry Box {
              size 0.02 0.02 0.02
            }
          }
        ]
      }
      Pose {
        translation %<= (fields.translationX.value) >% %<= (fields.translationYRight.value) >% %<= (fields.translationZ.value) >%
        children[
          Shape {
            appearance MattePaint {
              baseColor 1 1 1
            }
            geometry Box {
              size 0.02 0.02 0.02
            }
          }
        ]
      }
      Camera {
        translation %<= (fields.translationX.value) >% %<= (fields.translationYLeft.value) >% %<= (fields.translationZ.value) >%
        rotation %<= (fields.rotationX.value) >% %<= (fields.rotationY.value) >% %<= (fields.rotationZ.value) >% %<= (fields.rotationW.value) >%
        name %<= '"' + fields.name.value + '_left"' >%
        fieldOfView 1.57
        width 640
        height 480
        noise IS colorNoise
      }
      Camera {
        translation %<= (fields.translationX.value) >% %<= (fields.translationYRight.value) >% %<= (fields.translationZ.value) >%
        rotation %<= (fields.rotationX.value) >% %<= (fields.rotationY.value) >% %<= (fields.rotationZ.value) >% %<= (fields.rotationW.value) >%
        name %<= '"' + fields.name.value + '_right"' >%
        fieldOfView 1.57
        width 640
        height 480
        noise IS colorNoise
      }
    ]
    name IS name
    boundingObject Group {
      children [
        Pose {
          translation 0.00000 0.000000 0.0
          children [
            Box {
              size 0.02 0.08 0.02
            }
          ]
        }

      ]
    }
    physics Physics {
      density -1
      mass 0.19
    }
  }
}
