#VRML_SIM R2023a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://www.cyberbotics.com/doc/guide/lidar-sensors#velodyne-puck
# Velodyne Puck sensor model based on the Lidar PROTO.


PROTO Lidar3D [
  field   SFVec3f    translation    0 0 0
  field   SFRotation rotation       0 0 1 0
  field   SFString   name           "Velodyne"
]
{
  Lidar {
    translation IS translation
    rotation IS rotation
    children [
      DEF BOTTOM Transform {
        translation 0 0 -0.0286
        children [
          Shape {
            appearance DEF COLOR PBRAppearance {
              baseColor 0.6 0.6 0.6
              roughness 0.5
            }
            geometry Cylinder {
              height 0.0188
              radius 0.05165
              subdivision 24
            }
          }
        ]
      }
      DEF LOGO Transform {
        translation 0 0 -0.0286
        rotation 0 0 1 1.5708
        children [
          Shape {
            appearance USE COLOR
            geometry Cylinder {
              bottom FALSE
              top FALSE
              height 0.0188
              radius 0.052
              subdivision 24
            }
          }
        ]
      }
      DEF GLASS Transform {
        translation 0 0.0004 0
        children [
          Shape {
            appearance PBRAppearance {
              baseColor 0.0470588 0.0470588 0.0470588
              metalness 0
              roughness 0.1
            }
            geometry Cylinder {
              height 0.0392
              radius 0.05165
              subdivision 24
            }
          }
        ]
      }
      DEF TOP Transform {
        translation 0 0 0.0274
        children [
          Shape {
            appearance USE COLOR
            geometry Cylinder {
              height 0.0147
              radius 0.05165
              subdivision 24
            }
          }
        ]
      }
    ]
    name IS name
    model "Velodyne"
    boundingObject Transform {
      translation 0 -0.00165 0
      children [
        Cylinder {
          height 0.0727
          radius 0.052
        }
      ]
    }
    physics Physics {
      density -1
        mass 0.590
    }
    horizontalResolution 1800
    fieldOfView 6.28318
    verticalFieldOfView 0.3491
    numberOfLayers 16
    minRange 1
    maxRange 100
    projection "cylindrical"
    type "rotating"
    noise 0.003 # 0.03 / 100
    defaultFrequency 5
    minFrequency 5
    maxFrequency 20
  }
}
