#VRML_SIM R2025a utf8
# license: Apache License 2.0
# license url: https://www.apache.org/licenses/LICENSE-2.0
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/robots/robotis/turtlebot/protos/TurtleBot3Burger.proto
# keywords: robot/wheeled
# Burger model of the third version of the TurtleBot robot.

EXTERNPROTO "sensor_protos/DiffWheel.proto"
EXTERNPROTO "sensor_protos/EngagedWheel.proto"
EXTERNPROTO "sensor_protos/RGBD.proto"
EXTERNPROTO "sensor_protos/Lidar2D.proto"
EXTERNPROTO "sensor_protos/IMU.proto"

PROTO Cruzr [
  field SFVec3f    translation     0 0 0                 # Is `Pose.translation`.
  field SFRotation rotation        0 1 0 0               # Is `Pose.rotation`.
  field SFString   name            "Cruzr"    # Is `Solid.name`.
  field SFString   controller      "<generic>"           # Is `Robot.controller`.
  field MFString   controllerArgs  []                    # Is `Robot.controllerArgs`.
  field SFString   customData      ""                    # Is `Robot.customData`.
  field SFBool     supervisor      TRUE                 # Is `Robot.supervisor`.
  field SFBool     synchronization TRUE                  # Is `Robot.synchronization`.
  field SFFloat    wheelRadius     0.05
  field SFFloat    wheelThickness    0.03
  field SFFloat    wheelToCenterDis      0.2
  field MFNode     extensionSlot   [

  ]  # Extends the robot with new nodes in the extension slot.
]
{
  Robot {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    customData IS customData
    supervisor IS supervisor
    synchronization IS synchronization
    children [
      Pose {
        translation 0.0 0 0.0
        children IS extensionSlot
      }
      DEF DIFFWHEEL_ROBOT Group {
        children [
          DEF RIGHT_JOINT DiffWheel {
            isLeftWheel TRUE
            wheelRadius IS wheelRadius
            wheelThickness IS wheelThickness
            wheelToCenterDis IS wheelToCenterDis
          }
          DEF LEFT_JOINT DiffWheel {
            isLeftWheel FALSE
            wheelRadius IS wheelRadius
            wheelThickness IS wheelThickness
            wheelToCenterDis IS wheelToCenterDis
          }
          EngagedWheel {
            wheelRadius 0.005
            translationX 0.2
            translationY 0.0
          }
          EngagedWheel {
            wheelRadius 0.005
            translationX -0.2
            translationY 0.0
          }
        ]
      }
      Lidar2D {
        translation 0 0 0.21
        name "lidar"
      }
      RGBD {
        translation 0.22 0 0.56
        rotation 0 0 1 0
        name "rgbd"
      }
      IMU {
        translation 0.0 0.0 0.1
        name "imu"
      }
      DEF BODY Pose {
        translation 0 0 0.01
        rotation 0 0 1 1.57
        children [
          CadShape {
            url "meshes/cruzr/base_link.dae"
          }
        ]
      }
      DEF HEAD Pose {
        translation 0 0 1.0
        rotation 0 0 1 1.57
        children [
          CadShape {
            url "meshes/cruzr/head.dae"
          }
        ]
      }
      Accelerometer {
        translation -0.032 0 0.078
      }
      Gyro {
        translation -0.032 0 0.078
      }
      Compass {
        translation -0.032 0 0.078
      }
    ]
    boundingObject Group {
      children [
        Pose {
          translation -0.032 0 0.51
          children [
            Box {
              size 0.097 0.137 1
            }
          ]
        }
        Pose {
          translation -0.032 0 0.51
          children [
            Box {
              size 0.135 0.095 1
            }
          ]
        }
      ]
    }
    physics Physics {
      density -1
      mass 20
      centerOfMass [
        -0.035 0 0.03
      ]
    }
  }
}
