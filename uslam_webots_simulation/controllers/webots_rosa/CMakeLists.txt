
find_package(rosa REQUIRED)
find_package(rosa_tf REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(mc_task_msgs REQUIRED)
find_package(rosgraph_msgs REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(shm_msgs REQUIRED)
find_package(Eigen3 REQUIRED)
find_package(PCL REQUIRED)
find_package(pcl_conversions REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(OpenCV REQUIRED)

add_executable(
  webots_rosa
  controller.cpp
  src/utils.cpp
  src/sensor_base.cpp
  src/imu.cpp
  src/rgbd.cpp
  src/camera.cpp
  src/stereo_camera.cpp
  src/lidar_2d.cpp
  src/lidar_3d.cpp
  src/range_finder.cpp
  src/robot_base.cpp
  src/diff_wheel_robot.cpp
  src/mecanum_wheel_robot.cpp
  src/webots_module.cpp)

target_include_directories(webots_rosa PUBLIC
  $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
  $<BUILD_INTERFACE:${WEBOTS_INCLUDES}>
  $<BUILD_INTERFACE:${WEBOTS_HOME}/include/controller/c>
)

target_link_libraries(webots_rosa Eigen3::Eigen ${WEBOTS_LIBRARIES} ${PCL_LIBRARIES} ${OpenCV_LIBS} yaml-cpp::yaml-cpp)

ament_target_dependencies(webots_rosa
  rosa
  sensor_msgs
  rosgraph_msgs
  geometry_msgs
  nav_msgs
  shm_msgs
  rosa_tf
  pcl_conversions
  mc_task_msgs
  cv_bridge
)

install(TARGETS webots_rosa
  RUNTIME DESTINATION share/${PROJECT_NAME}/controllers/webots_rosa)
install(DIRECTORY config/ DESTINATION share/${PROJECT_NAME}/controllers/webots_rosa/config)

if(INSTALL_CONTROLLER_TO_SOURCE_DIR)
  add_custom_command(
    TARGET webots_rosa
    POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/webots_rosa
    ${CMAKE_CURRENT_SOURCE_DIR})
endif()
