/**
 *
 *   @file control_manager_module.h
 *   <AUTHOR> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2022-11-24
 *
 *   (C) 2022 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef WEBOTS_MODULE_H_
#define WEBOTS_MODULE_H_
#include <thread>
#include <vector>
#include <atomic>

#include <webots/Robot.hpp>
#include <webots/vehicle/Car.hpp>
#include <webots/vehicle/Driver.hpp>

#include <Eigen/Core>

#include <rosa/rosa.h>
#include <geometry_msgs/msg/Twist.h>
#include <rosgraph_msgs/msg/Clock.h>
#include <rosa/static_transform_broadcaster.h>

#include "sensor_base.hpp"
#include "robot_base.hpp"

namespace simulation {

class WebotsModule : public rosa::Node
{
public:
    WebotsModule();
    ~WebotsModule();

    bool init(const std::string &config_path);

private:
    int step();
    void update();
    void sendClock();
    void twistCallback(const geometry_msgs::msg::Twist::ConstSharedPtr msg);
    void publishData();
    void spin();

    webots::Robot *robot_;
    webots::Supervisor *super_robot_;

    int timestep_;
    std::string wb_name_;
    std::string robot_type_;

    std::string twist_in_topic_;
    rosa::Writer<rosgraph_msgs::msg::Clock>::SharedPtr clock_writer_;
    rosa::Reader<geometry_msgs::msg::Twist>::SharedPtr twist_reader_;
    std::shared_ptr<rosa::StaticTransformBroadcaster> static_tb_;

    // 基类指针，指向派生类实现多态
    std::shared_ptr<simulation::RobotBase> robot_model_;
    std::vector<std::shared_ptr<simulation::SensorBase>> sensor_list_;

    std::atomic<bool> should_shut_down_ = false; //线程的安全退出标志
    std::unique_ptr<std::thread> spin_thread_;  // 用线程来实现sycronization

    bool is_init = false;
};

}  // namespace simulation

#endif
