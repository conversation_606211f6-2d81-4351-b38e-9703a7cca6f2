#pragma once
#include <webots/Robot.hpp>
#include <webots/Motor.hpp>
#include <webots/Keyboard.hpp>

#include <Eigen/Core>
#include <Eigen/Dense>

#include <nav_msgs/msg/Odometry.h>
#include <geometry_msgs/msg/TransformStamped.h>
#include <rosa/transform_broadcaster.h>

#include "robot_base.hpp"

namespace simulation {

enum class MOTION_STATE_DIFFWHEEL {
    FORWARD = 0,
    BACKWARD = 1,
    LEFT = 2,
    RIGHT = 3,
    LEFT_TURN = 4,
    RIGHT_TURN = 5,
    STOP = 6,
    HEAD_PITCH_UP = 7,
    HEAD_PITCH_DOWN = 8,
    HEAD_YAW_LEFT = 9,
    HEAD_YAW_RIGHT = 10
};



class DiffWheelRobot : public RobotBase
{
public:
    DiffWheelRobot(webots::Robot *robot, int step, const YAML::Node &param);

    bool onInit() override;
    void setVelocity(const Eigen::Vector3d &linear, const Eigen::Vector3d &angular) override;
    void createCommunicationInterface(const rosa::Node::SharedPtr &node) override;
    void onPublish(const rosa::Time &time_stamp) override;
    
    void keyboard();

private:
    void updateRealOdom();
    void updateIntegralOdom(double left_speed, double right_speed, double left_encode, double right_encode);

    std::string motionStateToString(MOTION_STATE_DIFFWHEEL state);

    double WHEEL_RADIUS;
    double WHEEL_BASE;

    webots::Motor *left_wheel_motor_, *right_wheel_motor_;

    webots::Keyboard kb_;
    MOTION_STATE_DIFFWHEEL state_ = MOTION_STATE_DIFFWHEEL::STOP;

    double left_encode_old_, right_encode_old_;
    double postion_x_, postion_y_, postion_angular_;
    double last_odom_time_ = -1;

    std::string odom_out_topic_;
    bool publish_tf_, publish_map_odom_tf_, use_real_odom_and_pose_;
    rosa::Writer<nav_msgs::msg::Odometry>::SharedPtr odom_writer_;
    std::unique_ptr<rosa::TransformBroadcaster> tb_;
};

}  // namespace simulation
