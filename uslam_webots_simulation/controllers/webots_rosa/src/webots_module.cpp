#include "webots_module.h"

#include <string>

#include <pcl_conversions/pcl_conversions.h>

#include <yaml-cpp/yaml.h>

#include "imu.hpp"
#include "rgbd.hpp"
#include "camera.hpp"
#include "stereo_camera.hpp"
#include "lidar_2d.hpp"
#include "lidar_3d.hpp"

#include "mecanum_wheel_robot.hpp"
#include "diff_wheel_robot.hpp"

using namespace webots;

namespace simulation {

WebotsModule::WebotsModule() : rosa::Node("uslam_webots") {}

WebotsModule::~WebotsModule()
{
    should_shut_down_ = true;

    if (spin_thread_ != nullptr) {
        spin_thread_->join();
        spin_thread_.reset();
    }

    delete robot_;
    delete super_robot_;
}

bool WebotsModule::init(const std::string &config_path)
{   
    // 读取配置文件
    YAML::Node config = YAML::LoadFile(config_path);

    // wbt中机器人的创建绑定了这个控制器，这里新建的机器人对象能够自动绑定
    robot_ = new Robot();
    timestep_ = (int)robot_->getBasicTimeStep(); // 取整了
    wb_name_ = robot_->getName();

    if (!robot_->getSupervisor()) {
        ULOGF("Please set robot as supervisor.");
        return false;
    }

    ULOGI("getBasicTimeStep:%f name:%s", robot_->getBasicTimeStep(), wb_name_.c_str());

    //初始化机器人命令话题的接收者和clock发布者，还有tf的broadcaster
    twist_in_topic_ = config["twist_in_topic"].as<std::string>();

    auto node_ptr = std::static_pointer_cast<rosa::Node>(shared_from_this());
    clock_writer_ = createWriter<rosgraph_msgs::msg::Clock>("/clock", rosa::QoS(10));  //用来发布仿真时间
    twist_reader_ = createReader<geometry_msgs::msg::Twist>(
        twist_in_topic_, rosa::QoS(10), std::bind(&WebotsModule::twistCallback, this, std::placeholders::_1)); //订阅机器人控制信息
    static_tb_ = std::make_shared<rosa::StaticTransformBroadcaster>(*this);
    
    //初始化机器人模型和传感器
    const auto robot_sub_param = config["RobotModel"];
    robot_type_ = robot_sub_param["type"].as<std::string>();
    if (robot_type_ == "MecanumWheelRobot") {
        ULOGI("create robot model with type %s", robot_type_.c_str());
        robot_model_ = std::make_shared<simulation::MecanumWheelRobot>(robot_, timestep_, robot_sub_param);
    } else if (robot_type_ == "DiffWheelRobot") {
        ULOGI("create robot model with type %s", robot_type_.c_str());
        robot_model_ = std::make_shared<simulation::DiffWheelRobot>(robot_, timestep_, robot_sub_param);
    } else {
        ULOGI("Unsupport robot model type %s", robot_type_.c_str());
        abort();
    }

    ULOGI("init robot model");
    robot_model_->init();
    robot_model_->createCommunicationInterface(node_ptr);
    robot_model_->initStaticTB(static_tb_);

    ULOGI("init sensor list");
    std::vector<std::string> sensor_list_name;
    if (config["sensor_list"]) {
        for (const auto &item : config["sensor_list"]) {
            sensor_list_name.push_back(item.as<std::string>());
        }
    }
    
    for (auto sensor_name : sensor_list_name) {
        auto sub_param = config[sensor_name];
        std::string type;
        type = sub_param["type"].as<std::string>();
        if (type == "RGBD") {
            auto sensor = std::make_shared<simulation::RgbdSensor>(robot_, timestep_, sub_param);
            sensor_list_.push_back(std::move(sensor));
        }
        if (type == "Stereo") {
            auto sensor = std::make_shared<simulation::StereoCameraSensor>(robot_, timestep_, sub_param);
            sensor_list_.push_back(std::move(sensor));
        }
        if (type == "Camera") {
            auto sensor = std::make_shared<simulation::CameraSensor>(robot_, timestep_, sub_param);
            sensor_list_.push_back(std::move(sensor));
        }
        if (type == "IMU") {
            auto sensor = std::make_shared<simulation::IMUSensor>(robot_, timestep_, sub_param);
            sensor_list_.push_back(std::move(sensor));
        }
        if (type == "Lidar2d") {
            auto sensor = std::make_shared<simulation::Lidar2dSensor>(robot_, timestep_, sub_param);
            sensor_list_.push_back(std::move(sensor));
        }
        if (type == "Lidar3d") {
            auto sensor = std::make_shared<simulation::Lidar3dSensor>(robot_, timestep_, sub_param);
            sensor_list_.push_back(std::move(sensor));
        }
    }

    

    for (auto &sensor : sensor_list_) {
        sensor->init();
        sensor->createCommunicationInterface(node_ptr);
        sensor->initStaticTB(static_tb_);
    }

    //创建线程来运行
    spin_thread_ = std::make_unique<std::thread>(std::bind(&WebotsModule::spin, this));

    return true;
}

int WebotsModule::step()
{
    
    //推进仿真一个timestep，只有调用step，set的velocity才会生效；
    
    ULOGI_TIME(1s, "WebotsModule::step");
    if (robot_ != nullptr) {
        return robot_->step(timestep_);
    } else {
        ULOGE("robot_ is nullptr");
    }
    return -1;
}

void WebotsModule::spin()
{
    ULOGI("WebotsModule::spin");
    while (rosa::ok() && (!should_shut_down_) && step() != -1) {
        update();
    }
}

void WebotsModule::sendClock()
{
    ULOGI_TIME(1s, "WebotsModule::sendClock");
    if (robot_ == nullptr) {
        ULOGE("WebotsModule::sendClock failed because robot_ == nullptr");
        return;
    }
    const auto current_time = robot_->getTime();
    const auto timestamp = rosa::Time(static_cast<uint64_t>(current_time * 1e9));
    rosgraph_msgs::msg::Clock message = rosgraph_msgs::msg::Clock();
    message.clock.sec = timestamp.seconds();
    message.clock.nanosec = timestamp.nanoseconds() % 1000000000;
    clock_writer_->write(std::move(message));
}



void WebotsModule::twistCallback(const geometry_msgs::msg::Twist::ConstSharedPtr msg)
{
 
    //根据命令话题中的消息控制仿真中的机器人运行 在rosa的spin中不断进行

    ULOGI("Received twist msg linear: (%f, %f, %f) angular: (%f, %f, %f)", msg->linear.x, msg->linear.y, msg->linear.z,
          msg->angular.x, msg->angular.y, msg->angular.z);
    Eigen::Vector3d linear(msg->linear.x, msg->linear.y, msg->linear.z);
    Eigen::Vector3d angular(msg->angular.x, msg->angular.y, msg->angular.z);
    if (robot_model_ != nullptr) {
        robot_model_->setVelocity(linear, angular);  // 具体robot类型调用相应的函数，多态
    } else {
        ULOGE("robot_model_ is nullptr");
    }
}

void WebotsModule::publishData()
{
    ULOGI_TIME(1s, "WebotsModule::publishData start");
    const auto time_stamp = rosa::Time::now();
    std::vector<std::thread> threads;

    threads.emplace_back(&simulation::RobotBase::publishValue, robot_model_, time_stamp);
    for (auto &sensor : sensor_list_) {
        threads.emplace_back(&simulation::SensorBase::publishValue, sensor, time_stamp);
    }

    // 等待所有线程完成
    for (auto &thread : threads) {
        thread.join();
    }
    ULOGI_TIME(1s, "WebotsModule::publishData done");
}

void WebotsModule::update()
{
    ULOGI_TIME(1s, "WebotsModule::update");

    sendClock();

    publishData();
}
}  // namespace simulation
