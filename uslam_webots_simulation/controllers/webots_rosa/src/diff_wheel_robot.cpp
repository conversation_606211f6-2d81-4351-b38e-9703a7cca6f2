#include "diff_wheel_robot.hpp"

#include <webots/PositionSensor.hpp>

#include "utils.hpp"

namespace simulation {

DiffWheelRobot::DiffWheelRobot(webots::Robot *robot, int step, const YAML::Node &param) : RobotBase(robot, step, param)
{
}

bool DiffWheelRobot::onInit()
{
    odom_out_topic_ = param_["odom_out_topic"].as<std::string>();
    publish_tf_ = param_["publish_tf"].as<bool>();
    publish_map_odom_tf_ = param_["publish_map_odom_tf"].as<bool>();
    use_real_odom_and_pose_ = param_["use_real_odom_and_pose"].as<bool>();

    // keyborad input enable
    kb_.enable(robot_->getBasicTimeStep());
    //ULOGI("DiffWheelRobot::Init odom_out_topic: %s publish_tf: %d", odom_out_topic_.c_str(), publish_tf_);

    WHEEL_RADIUS = super_robot_->getSelf()->getField("wheelRadius")->getSFFloat();
    WHEEL_BASE = super_robot_->getSelf()->getField("wheelToCenterDis")->getSFFloat() * 2.0;
    ULOGI("WHEEL_RADIUS:%f WHEEL_BASE:%f", WHEEL_RADIUS, WHEEL_BASE);

    left_wheel_motor_ = robot_->getMotor("left wheel motor");
    right_wheel_motor_ = robot_->getMotor("right wheel motor");

    if (!left_wheel_motor_ || !right_wheel_motor_) {
        ULOGF("DiffWheelRobot: left_wheel_motor_ or right_wheel_motor_ is not found.");
        return false;
    }

    if (!left_wheel_motor_->getPositionSensor() || !right_wheel_motor_->getPositionSensor()) {
        ULOGF("DiffWheelRobot: left_wheel_sensor or right_wheel_sensor is not found.");
        return false;
    }

    left_wheel_motor_->setVelocity(0.0);
    left_wheel_motor_->setPosition(std::numeric_limits<double>::infinity());
    left_wheel_motor_->getPositionSensor()->enable(robot_->getBasicTimeStep());
    right_wheel_motor_->setVelocity(0.0);
    right_wheel_motor_->setPosition(std::numeric_limits<double>::infinity());
    right_wheel_motor_->getPositionSensor()->enable(robot_->getBasicTimeStep());

    postion_x_ = 0.0;
    postion_y_ = 0.0;
    postion_angular_ = 0.0;
    right_encode_old_ = 0.0;
    left_encode_old_ = 0.0;

    return true;
}

void DiffWheelRobot::createCommunicationInterface(const rosa::Node::SharedPtr &node)
{
    odom_writer_ = node->createWriter<nav_msgs::msg::Odometry>(odom_out_topic_, rosa::QoS(10));
    tb_ = std::make_unique<rosa::TransformBroadcaster>(*node);
    //ULOGI("DiffWheelRobot::Init odom_out_topic: %s publish_tf: %d", odom_out_topic_.c_str(), publish_tf_);
}

void DiffWheelRobot::onPublish(const rosa::Time &time_stamp)
{
    if (use_real_odom_and_pose_) {
        updateRealOdom();
    } else {
        updateIntegralOdom(left_wheel_motor_->getVelocity(), right_wheel_motor_->getVelocity(),
                           left_wheel_motor_->getPositionSensor()->getValue(),
                           right_wheel_motor_->getPositionSensor()->getValue());
    }

    keyboard();
}

void DiffWheelRobot::setVelocity(const Eigen::Vector3d &linear, const Eigen::Vector3d &angular)
{
    double left_speed = (linear.x() - angular.z() * WHEEL_BASE / 2) / WHEEL_RADIUS;
    double right_speed = (linear.x() + angular.z() * WHEEL_BASE / 2) / WHEEL_RADIUS;
    ULOGI("DiffWheelRobot: Twist left:%f right:%f", left_speed, right_speed);
    left_wheel_motor_->setVelocity(left_speed);
    right_wheel_motor_->setVelocity(right_speed);
    ULOGI("DiffWheelRobot: Twist vx:%f vz:%f", linear.x(), angular.z());
}

void DiffWheelRobot::updateRealOdom()
{
    Eigen::Vector3d t, lv, av;
    Eigen::Matrix3d R;

    getRobotPose(R, t, lv, av);

    nav_msgs::msg::Odometry odom_msg;
    odom_msg.header.frame_id = "odom";
    odom_msg.header.stamp = rosa::Time::now();
    odom_msg.child_frame_id = "base_link";

    Eigen::Matrix4d Twb = Eigen::Matrix4d::Identity();
    Twb(0, 3) = t(0);
    Twb(1, 3) = t(1);
    Twb(2, 3) = t(2);
    Twb.block<3, 3>(0, 0) = R;
    Eigen::Matrix4d Tob = Two_.inverse().eval() * Twb;
    odom_msg.pose.pose.position.x = Tob(0, 3);
    odom_msg.pose.pose.position.y = Tob(1, 3);
    odom_msg.pose.pose.position.z = Tob(2, 3);
    Eigen::Quaterniond q(Tob.block<3, 3>(0, 0));
    odom_msg.pose.pose.orientation.x = q.x();
    odom_msg.pose.pose.orientation.y = q.y();
    odom_msg.pose.pose.orientation.z = q.z();
    odom_msg.pose.pose.orientation.w = q.w();

    Eigen::Matrix3d Rbw = Twb.block<3, 3>(0, 0).inverse().eval();
    Eigen::Vector3d linear_v = Rbw * lv;
    Eigen::Vector3d angular_v = Rbw * av;
    odom_msg.twist.twist.linear.x = linear_v.x();
    odom_msg.twist.twist.linear.y = linear_v.y();
    odom_msg.twist.twist.linear.z = linear_v.z();
    odom_msg.twist.twist.angular.x = angular_v.x();
    odom_msg.twist.twist.angular.y = angular_v.y();
    odom_msg.twist.twist.angular.z = angular_v.z();
    odom_writer_->write(odom_msg);

    if (publish_tf_) {
        geometry_msgs::msg::TransformStamped msg;
        msg.header.frame_id = "/odom";
        msg.header.stamp = rosa::Time::now();
        msg.child_frame_id = "/base_link";
        msg.transform.translation.x = Tob(0, 3);
        msg.transform.translation.y = Tob(1, 3);
        msg.transform.translation.z = Tob(2, 3);
        msg.transform.rotation.x = q.x();
        msg.transform.rotation.y = q.y();
        msg.transform.rotation.z = q.z();
        msg.transform.rotation.w = q.w();
        tb_->sendTransform(msg);

        if (publish_map_odom_tf_) {
            geometry_msgs::msg::TransformStamped tf_map_odom;
            tf_map_odom.header.frame_id = "/map";
            tf_map_odom.header.stamp = rosa::Time::now();
            tf_map_odom.child_frame_id = "/odom";
            tf_map_odom.transform.translation.x = 0;
            tf_map_odom.transform.translation.y = 0;
            tf_map_odom.transform.translation.z = 0;
            tf_map_odom.transform.rotation.x = 0;
            tf_map_odom.transform.rotation.y = 0;
            tf_map_odom.transform.rotation.z = 0;
            tf_map_odom.transform.rotation.w = 1;
            tb_->sendTransform(tf_map_odom);
        }
    }
}

void DiffWheelRobot::updateIntegralOdom(double left_speed, double right_speed, double left_encode, double right_encode)
{   
    
        //根据电机的运转算出现在的相对于初始位置的位姿
    
    double speed_liner = (left_speed + right_speed) * WHEEL_RADIUS / 2.0;
    double speed_angular = (right_speed - left_speed) * WHEEL_RADIUS / WHEEL_BASE;
    double left_encode_diff = (left_encode - left_encode_old_);
    double right_encode_diff = (right_encode - right_encode_old_);
    left_encode_old_ = left_encode;
    right_encode_old_ = right_encode;

    double diff_x = (left_encode_diff + right_encode_diff) * WHEEL_RADIUS / 2.0;
    double diff_angular = (right_encode_diff - left_encode_diff) * WHEEL_RADIUS / WHEEL_BASE;

    postion_angular_ += diff_angular;
    if (postion_angular_ > M_PI) postion_angular_ = postion_angular_ - 2 * M_PI;
    if (postion_angular_ < (-M_PI)) postion_angular_ = postion_angular_ + 2 * M_PI;
    postion_x_ += (diff_x * cos(postion_angular_));
    postion_y_ += (diff_x * sin(postion_angular_));

    nav_msgs::msg::Odometry odom_msg;

    odom_msg.header.stamp = rosa::Time::now();
    odom_msg.header.frame_id = "odom";
    odom_msg.child_frame_id = "base_link";
    odom_msg.pose.pose.position.x = postion_x_;
    odom_msg.pose.pose.position.y = postion_y_;
    odom_msg.pose.pose.position.z = 0;
    odom_msg.pose.pose.orientation = createQuaternionMsgFromYaw(postion_angular_);
    odom_msg.twist.twist.linear.x = speed_liner;
    odom_msg.twist.twist.linear.y = 0;
    odom_msg.twist.twist.angular.z = speed_angular;
    odom_writer_->write(odom_msg);

    if (publish_tf_) {
        geometry_msgs::msg::TransformStamped msg;
        msg.header.stamp = rosa::Time::now();
        msg.header.frame_id = "odom";
        msg.child_frame_id = "base_link";
        msg.transform.translation.x = odom_msg.pose.pose.position.x;
        msg.transform.translation.y = odom_msg.pose.pose.position.y;
        msg.transform.translation.z = 0;
        msg.transform.rotation = createQuaternionMsgFromYaw(postion_angular_);
        tb_->sendTransform(msg);

        if (publish_map_odom_tf_) {
            geometry_msgs::msg::TransformStamped tf_map_odom;
            tf_map_odom.header.frame_id = "/map";
            tf_map_odom.header.stamp = rosa::Time::now();
            tf_map_odom.child_frame_id = "/odom";
            tf_map_odom.transform.translation.x = 0;
            tf_map_odom.transform.translation.y = 0;
            tf_map_odom.transform.translation.z = 0;
            tf_map_odom.transform.rotation.x = 0;
            tf_map_odom.transform.rotation.y = 0;
            tf_map_odom.transform.rotation.z = 0;
            tf_map_odom.transform.rotation.w = 1;
            tb_->sendTransform(tf_map_odom);
        }
    }
}

void DiffWheelRobot::keyboard()
{
    static double vx = 0.2;
    static double vy = 0.2;
    static double vz = 0.2;

    static double curr_x = 0.0;
    static double curr_y = 0.0;
    static double curr_z = 0.0;

    static double curr_pitch = 0.0;
    static double curr_yaw = 0.0;

    static double max_linear_v = 0.90;
    static double max_angular_v = 2.0;
    static double min_linear_v = 0.1;
    static double min_angular_v = 0.1;

    int key = kb_.getKey();

    if (key == -1) {
        return;
    }

    // x方向线速度加减速
    if (key == 81) {  // q x方向加速
        if (vx < max_linear_v) {
            vx += 0.1;
            ULOGI_S() << "increase vx 0.1 to " << vx;
        } else {
            ULOGE_S() << "can not increase vx because it exceeds max velocity = " << max_linear_v;
        }
    }
    if (key == 90) {  // z x方向加速
        if (vx > min_linear_v) {
            vx -= 0.1;
            ULOGI_S() << "decrease vx 0.1 to " << vx;
        } else {
            ULOGE_S() << "can not decrease vx because it exceeds min velocity = " << max_linear_v;
        }
    }

    // y方向线速度加减速
    if (key == 87) {  // w y方向加速
        if (vy < max_linear_v) {
            vy += 0.1;
            ULOGI_S() << "increase vy 0.1 to " << vy;
        } else {
            ULOGE_S() << "can not increase vy because it exceeds max velocity = " << max_linear_v;
        }
    }
    if (key == 88) {  // x y方向减速
        if (vy > min_linear_v) {
            vy -= 0.1;
            ULOGI_S() << "decrease vy 0.1 to " << vy;
        } else {
            ULOGE_S() << "can not decrease vy because it exceeds min velocity = " << max_linear_v;
        }
    }

    // 角速度加减速
    if (key == 69) {  // e angular加速
        if (vz < max_angular_v) {
            vz += 0.1;
            ULOGI_S() << "increase vz 0.1 to " << vz;
        } else {
            ULOGE_S() << "can not increase vz because it exceeds max velocity = " << max_angular_v;
        }
    }
    if (key == 67) {  // c angular减速
        if (vz > min_angular_v) {
            vz -= 0.1;
            ULOGI_S() << "decrease vz 0.1 to " << vz;
        } else {
            ULOGE_S() << "can not decrease vz because it exceeds min velocity = " << min_angular_v;
        }
    }

    // 修改运动状态
    if (key == 315) {  // 上
        state_ = MOTION_STATE_DIFFWHEEL::FORWARD;
        // ULOGI_S() << "FORWARD";
    } else if (key == 317) {  // 下
        state_ = MOTION_STATE_DIFFWHEEL::BACKWARD;
        ULOGI_S() << "BACKWARD";
    } else if (key == 314) {  // 左
        state_ = MOTION_STATE_DIFFWHEEL::LEFT;
        ULOGI_S() << "LEFT";
    } else if (key == 316) {  // 右
        state_ = MOTION_STATE_DIFFWHEEL::RIGHT;
    } else if (key == 65) {  // a 左转
        state_ = MOTION_STATE_DIFFWHEEL::LEFT_TURN;
    } else if (key == 68) {  // d 右转
        state_ = MOTION_STATE_DIFFWHEEL::RIGHT_TURN;
    } else if (key == 32) {  // space 停止
        state_ = MOTION_STATE_DIFFWHEEL::STOP;
    // } else if (key == 73) {  // I 头部向上
    //     state_ = MOTION_STATE::HEAD_PITCH_UP;
    // } else if (key == 75) {  // K 头部向上
    //     state_ = MOTION_STATE::HEAD_PITCH_DOWN;
    // } else if (key == 76) {  // L 头部向左
    //     state_ = MOTION_STATE::HEAD_YAW_LEFT;
    // } else if (key == 74) {  // J 头部向右
    //     state_ = MOTION_STATE::HEAD_YAW_RIGHT;
    }
    switch (state_) {
        case MOTION_STATE_DIFFWHEEL::FORWARD:
            curr_x = vx;
            curr_y = 0.0;
            curr_z = 0.0;
            ULOGI_S() << "FORWARD";
            break;
        case MOTION_STATE_DIFFWHEEL::BACKWARD:
            curr_x = -vx;
            curr_y = 0.0;
            curr_z = 0.0;
            break;
        case MOTION_STATE_DIFFWHEEL::LEFT:
            curr_x = 0.0;
            curr_y = vy;
            curr_z = 0.0;
            break;
        case MOTION_STATE_DIFFWHEEL::RIGHT:
            curr_x = 0.0;
            curr_y = -vy;
            curr_z = 0.0;
            break;
        case MOTION_STATE_DIFFWHEEL::LEFT_TURN:
            curr_x = 0.0;
            curr_y = 0.0;
            curr_z = vz;
            break;
        case MOTION_STATE_DIFFWHEEL::RIGHT_TURN:
            curr_x = 0.0;
            curr_y = 0.0;
            curr_z = -vz;
            break;
        case MOTION_STATE_DIFFWHEEL::STOP:
            curr_x = 0.0;
            curr_y = 0.0;
            curr_z = 0.0;
            curr_pitch = 0.0;
            curr_yaw = 0.0;
            break;
        // case MOTION_STATE::HEAD_PITCH_UP:
        //     curr_pitch = 0.8;
        //     break;
        // case MOTION_STATE::HEAD_PITCH_DOWN:
        //     curr_pitch = -0.8;
        //     break;
        // case MOTION_STATE::HEAD_YAW_LEFT:
        //     curr_yaw = -0.8;
        //     break;
        // case MOTION_STATE::HEAD_YAW_RIGHT:
        //     curr_yaw = 0.8;
        //     break;
        default:
            break;
    }
    Eigen::Vector3d linear(curr_x, curr_y, 0.0);
    Eigen::Vector3d angular(0.0, 0.0, curr_z);
    setVelocity(linear, angular);
    // setHeadVelocity(curr_pitch, curr_yaw, 0.3, 0.3);
}


std::string DiffWheelRobot::motionStateToString(MOTION_STATE_DIFFWHEEL state)
{
    switch (state) {
        case MOTION_STATE_DIFFWHEEL::FORWARD:
            return "FORWARD";
        case MOTION_STATE_DIFFWHEEL::BACKWARD:
            return "BACKWARD";
        case MOTION_STATE_DIFFWHEEL::LEFT:
            return "LEFT";
        case MOTION_STATE_DIFFWHEEL::RIGHT:
            return "RIGHT";
        case MOTION_STATE_DIFFWHEEL::LEFT_TURN:
            return "LEFT_TURN";
        case MOTION_STATE_DIFFWHEEL::RIGHT_TURN:
            return "RIGHT_TURN";
        case MOTION_STATE_DIFFWHEEL::STOP:
            return "STOP";
        // case MOTION_STATE::HEAD_PITCH_UP:
        //     return "HEAD_PITCH_UP";
        // case MOTION_STATE::HEAD_PITCH_DOWN:
        //     return "HEAD_PITCH_DOWN";
        // case MOTION_STATE::HEAD_YAW_LEFT:
        //     return "HEAD_YAW_LEFT";
        // case MOTION_STATE::HEAD_YAW_RIGHT:
        //     return "HEAD_YAW_RIGHT";
        default:
            return "UNKNOWN";
    }
}

}  // namespace simulation
