// File:          vnav_controller.cpp
// Date:
// Description:
// Author:
// Modifications:

// You may need to add webots include files such as
// <webots/DistanceSensor.hpp>, <webots/Motor.hpp>, etc.
// and/or to add some other includes
#include <webots/Robot.hpp>

#include "webots_module.h"

// All the webots classes are defined in the "webots" namespace
using namespace webots;

// This is the main program of your controller.
// It creates an instance of your Robot instance, launches its
// function(s) and destroys it at the end of the execution.
// Note that only one instance of Robot should be created in
// a controller program.
// The arguments of the main function can be specified by the
// "controllerArgs" field of the Robot node
int main(int argc, char **argv)
{   
    // args are set in the robot filed at the end of the .wbt file.
    if (argc != 2) {
        ULOGF("ERROR: please set controller args.\n");
        return -1;
    }

    rosa::init(argc, argv);

    std::string exec_name(argv[0]); 
    std::string config_path(argv[1]);  
    std::size_t found = exec_name.find_last_of("/");
    //可执行程序的文件明要与功能包的名称一致，帮助找到config文件夹的路径
    if (found) {
        std::string exec_base_path = exec_name.substr(0, found + 1);
        config_path = exec_base_path + "config/" + config_path;
    }
    ULOGI("INFO: config_path:%s\n", config_path.c_str());

    auto wb_module = std::make_shared<simulation::WebotsModule>();
    if (!wb_module->init(config_path)) {
        return -1;
    }
    rosa::spin(wb_module);
    rosa::shutdown();
    ULOGI("INFO: exit\n");
    return 0;
}
