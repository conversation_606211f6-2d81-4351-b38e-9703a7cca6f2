# uslam_webots_simulation 
基于rosa的webots仿真环境

## 说明
该包是webots仿真环境, 接入了rosa接口 \
依赖: ubuntu 22.04, rosa, webots 2023 b;

### 仿真环境内容和使用介绍：[文档链接](https://ew9vfg36e3.feishu.cn/docx/WlItda16RoBt5ExVkIHcY0ZjnIA?from=from_copylink); 

## udoke模板

```yaml
---
nodes:
  core:
    command: bash
    environment:
      NVIDIA_DRIVER_CAPABILITIES: all
      NVIDIA_VISIBLE_DEVICES: all
      DISPLAY: ${DISPLAY}
    volumes_from:
      - glcr.rd.ubtrobot.com/rosa/rosa:ubuntu22.04 /opt/rosa
      - glcr.rd.ubtrobot.com/t800/common/image/3rdparty:ubuntu22.04 /opt/ubt_3rdparty
    image: glcr.rd.ubtrobot.com/t800/common/image/base:ubuntu22.04
    network_mode: host
    shm_size: 8g
    tty: true
    volumes:
      - [~/Workspace, ~/workspace, rw]
      - [/usr/local/cuda, /usr/local/cuda, ro]
    working_dir: "~"
    use_dev: true
    use_x11: true
    use_gpus: true
    install_before_starting:
      apt: ["clang-format"]
      pip: ["cmake-format"] # 请确保容器中有pip
    source:
      bash: [source /opt/rosa/setup.bash]
      zsh: [source /opt/rosa/setup.zsh]
```

## 拉取依赖三方包
```
bash before_build.sh
```

## 编译
```
bash build.sh
```

## 运行
```
bash uslam_webots_simulation/scripts/run_webots.sh
```

进入webot界面后选择 File -> Open world , 选择

+ `uslam_webots_simulation/worlds/big_room.wbt` : 大房间
+ `uslam_webots_simulation/worlds/break_room.wbt` : 小房间

## 机器人控制

### 方法一 
安装 teleop_twist_keyboard
```
sudo apt install ros-jazzy-teleop-twist-keyboard
```

运行键盘遥控节点
```
ros2 run teleop_twist_keyboard teleop_twist_keyboard
```
### 方法二 (只适配mecanumbot 底盘)
+ 点击webots 界面, a,d 是左右方向键盘, 方向键盘移动, w,s 加减速

## 机器人配置

### 工程目录结构

* controllers：webots机器人控制目录
* protos：机器和传感器模型文件
* worlds：仿真场景文件


### 机器人添加

官方示例放在`/usr/local/webots/projects/`中，如室内场景示例在`/usr/local/webots/projects/samples/environments/indoor/worlds/`，可从中拷贝出来打开并加入自己的机器人。

参考`uslam4_webots_simulation/worlds/break_room.wbt`，在文件末端加入需要的机器人及相关配置

**Cadebot：**

```
Cadebot {
  translation -4.855024195917629e-07 1.6204759550183643e-12 -0.002880162269522396
  rotation -1.5977513231667601e-06 0.9999999999987236 6.6937581678412685e-09 0.0022458342765532564
  controller "webots_ros2"
  controllerArgs [
    "cadebot.yaml"
  ]
}
```

**Cruzr**

```
Cruzr {
  translation -1 1.6204759550183643e-12 -0.002880162269522396
  rotation -1.5977513231667601e-06 0.9999999999987236 6.6937581678412685e-09 0.0022458342765532564
  controller "webots_ros2"
  controllerArgs [
    "cruzr.yaml"
  ]
}
```

**MecanumBot**

```
MecanumBot {
  translation -8.759913197783787 -4.890417357858404 0.10242422773227479
  rotation 0.7564502141917597 -0.6510926832486681 0.06214009389508892 0.0005215062180802325
  controller "webots_ros2"
  controllerArgs [
    "mecanumbot.yaml"
  ]
}
```
### 机器人底盘和传感器配置
#### 机器人底盘
- diff_wheel_robot
- mecanum_wheel_robot
#### 传感器
- camera
- depth
- stereo_camera
- imu
- lidar_2d
- lidar_3d
- rgbd


当前可用场景, 具体说明见文档：
big_room
big_room_diff
break_room
factory_example

